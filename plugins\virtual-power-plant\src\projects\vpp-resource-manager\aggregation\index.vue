<template>
  <div class="aggregation-container">
    <!-- 搜索和筛选区域 -->
    <div class="search-filter-container">
      <!-- 搜索框和筛选器 -->
      <div class="search-filter-left">
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          :placeholder="$T('请输入关键字')"
          prefix-icon="el-icon-search"
          class="search-input"
        />
        <!-- 机组类型筛选 -->
        <CustomElSelect
          v-model="selectedType"
          :prefix_in="$T('机组类型')"
          class="filter-select"
        >
          <el-option
            v-for="option in typeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </CustomElSelect>
      </div>

      <!-- 新增按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          {{ $T("新增") }}
        </el-button>
      </div>
    </div>
    <!-- 表格区域 -->
    <div class="table-section">
      <el-table :data="currentPageData" class="data-table" max-height="640">
        <el-table-column prop="id" :label="$T('序号')" width="80">
          <template slot-scope="scope">
            {{ String(scope.row.id).padStart(2, "0") }}
          </template>
        </el-table-column>

        <el-table-column prop="name" :label="$T('机组名称')" min-width="200" />

        <el-table-column prop="type" :label="$T('机组类型')" min-width="150" />

        <el-table-column
          prop="resourceCount"
          :label="$T('聚合资源数量')"
          min-width="120"
        />

        <el-table-column :label="$T('操作')" width="200">
          <template slot-scope="scope">
            <div class="action-buttons-cell">
              <el-button
                type="text"
                class="action-btn detail-btn"
                @click="handleDetail(scope.row)"
              >
                {{ $T("详情") }}
              </el-button>
              <el-button
                type="text"
                class="action-btn edit-btn"
                @click="handleEdit(scope.row)"
              >
                {{ $T("编辑") }}
              </el-button>
              <el-button
                type="text"
                class="action-btn delete-btn"
                @click="handleDelete(scope.row)"
              >
                {{ $T("删除") }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <span>
        {{ $T("共") }}
        <span class="count-number">{{ totalCount }}</span>
        {{ $T("个") }}
      </span>
      <el-select v-model="pageSize" class="page-size-select">
        <el-option
          v-for="size in pageSizeOptions"
          :key="size"
          :label="`${size}${$T('条/页')}`"
          :value="size"
        />
      </el-select>
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="totalCount"
        layout="prev, pager, next, jumper"
        @current-change="handlePageChange"
        class="pagination-component"
      />
    </div>
    <!-- 新增/编辑弹窗 -->
    <AddUnitDialog
      :visible.sync="dialogVisible"
      :mode="dialogMode"
      :editData="currentEditData"
      @confirm="handleAddConfirm"
      @update="handleEditConfirm"
    />

    <!-- 详情抽屉 -->
    <DetailDrawer
      :visible.sync="detailDrawerVisible"
      :detail-data="currentDetailData"
      @resource-unbound="handleResourceUnbound"
    />
  </div>
</template>

<script>
import AddUnitDialog from "./components/AddUnitDialog.vue";
import DetailDrawer from "./components/DetailDrawer.vue";
import {
  listUnits,
  createUnit,
  updateUnit,
  deleteUnit,
  getUnit
} from "@/api/resource-aggregation";

export default {
  name: "VppResourceAggregation",
  components: {
    AddUnitDialog,
    DetailDrawer
  },
  data() {
    return {
      // 搜索和筛选
      searchKeyword: "",
      selectedType: "all",
      typeOptions: [
        { label: this.$T("全部"), value: "all" },
        { label: this.$T("调峰机组"), value: "peak_shaving" },
        { label: this.$T("调频机组"), value: "frequency_regulation" },
        { label: this.$T("需求响应机组"), value: "demand_response" },
        { label: this.$T("现货机组"), value: "spot_trading" },
        { label: this.$T("中长期机组"), value: "long_term_trading" }
      ],

      // 表格数据
      allTableData: [],
      totalCount: 0,

      // 分页
      currentPage: 1,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50, 100],

      // 弹窗控制
      dialogVisible: false,
      dialogMode: "add", // 'add' 或 'edit'
      currentEditData: {},

      // 详情抽屉
      detailDrawerVisible: false,
      currentDetailData: {}
    };
  },
  computed: {
    // 当前页数据
    currentPageData() {
      let filtered = this.allTableData;

      // 前端搜索过滤
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.trim().toLowerCase();
        filtered = filtered.filter(item =>
          item.name.toLowerCase().includes(keyword)
        );
      }

      return filtered;
    }
  },
  created() {
    this.loadUnits();
  },
  methods: {
    // 加载机组列表
    async loadUnits() {
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          unitType: this.selectedType
        };
        const response = await listUnits(params);

        if (response && response.data) {
          this.allTableData = response.data.list.map(item => ({
            id: item.unitId,
            name: item.unitName,
            type: this.getUnitTypeLabel(item.unitType),
            typeValue: item.unitType,
            resourceCount: item.resourceCount || 0
          }));

          // 如果API返回了total，使用API的total，否则使用list的长度
          this.totalCount =
            response.data.total !== undefined
              ? response.data.total
              : response.data.list.length;
        }
        // 生成100条mock数据
        const mockData = [];
        const unitTypes = [
          { type: "需求响应机组", typeValue: "demand_response" },
          { type: "调峰机组", typeValue: "peak_shaving" },
          { type: "调频机组", typeValue: "frequency_regulation" }
        ];

        const unitNamePrefixes = [
          "华能",
          "大唐",
          "华电",
          "国电",
          "中电投",
          "神华",
          "华润",
          "中广核",
          "国家能源",
          "三峡"
        ];
        const unitNameSuffixes = ["电厂", "发电厂", "热电厂", "电站", "发电站"];
        const unitNumbers = [
          "一号",
          "二号",
          "三号",
          "四号",
          "五号",
          "六号",
          "七号",
          "八号",
          "九号",
          "十号"
        ];

        for (let i = 1; i <= 100; i++) {
          const randomType =
            unitTypes[Math.floor(Math.random() * unitTypes.length)];
          const randomPrefix =
            unitNamePrefixes[
              Math.floor(Math.random() * unitNamePrefixes.length)
            ];
          const randomSuffix =
            unitNameSuffixes[
              Math.floor(Math.random() * unitNameSuffixes.length)
            ];
          const randomNumber =
            unitNumbers[Math.floor(Math.random() * unitNumbers.length)];

          mockData.push({
            id: String(i).padStart(4, "0"),
            name: `${randomPrefix}${randomSuffix}${randomNumber}机组`,
            type: randomType.type,
            typeValue: randomType.typeValue,
            resourceCount: Math.floor(Math.random() * 500) + 50 // 50-549之间的随机数
          });
        }

        this.allTableData = mockData;
        this.totalCount = mockData.length;
      } catch (error) {
        this.$message.error(this.$T("加载机组列表失败"));
        this.allTableData = [];
        this.totalCount = 0;
      }
    },

    // 新增
    handleAdd() {
      this.dialogMode = "add";
      this.currentEditData = {}; // 清空编辑数据
      this.dialogVisible = true;
    },

    // 新增确认
    async handleAddConfirm(data) {
      try {
        const requestData = {
          unitName: data.unitName,
          unitType: data.unitType,
          resourceIds: data.selectedResources.map(r => r.resourceId)
        };

        await createUnit(requestData);
        this.$message.success(this.$T("新增机组成功"));
        // 重新加载列表
        this.loadUnits();
      } catch (error) {
        this.$message.error(this.$T("新增机组失败"));
      }
    },

    // 获取机组类型标签
    getUnitTypeLabel(value) {
      const typeMap = {
        demand_response: this.$T("需求响应机组"),
        peak_shaving: this.$T("调峰机组"),
        frequency_regulation: this.$T("调频机组"),
        spot_trading: this.$T("现货机组"),
        long_term_trading: this.$T("中长期机组")
      };
      return typeMap[value] || value;
    },

    // 详情
    async handleDetail(row) {
      try {
        // const response = await getUnit(row.id);
        // if (response && response.data) {
        //   this.currentDetailData = response.data;
        //   this.detailDrawerVisible = true;
        // }
        this.currentDetailData = row;
        this.detailDrawerVisible = true;
      } catch (error) {
        this.$message.error(this.$T("获取机组详情失败"));
      }
    },

    // 编辑
    async handleEdit(row) {
      try {
        // 获取机组详情
        const response = await getUnit(row.id);
        if (response && response.data) {
          this.currentEditData = {
            unitId: response.data.unitId,
            unitName: response.data.unitName,
            unitType: response.data.unitType,
            selectedResources: response.data.boundResources || []
          };
          this.dialogMode = "edit";
          this.dialogVisible = true;
        }
      } catch (error) {
        this.$message.error(this.$T("获取机组详情失败"));
      }
    },

    // 编辑确认
    async handleEditConfirm(data) {
      try {
        const requestData = {
          unitName: data.unitName,
          resourceIds: data.selectedResources.map(
            r => r.resource_id || r.resourceId
          )
        };

        await updateUnit(this.currentEditData.unitId, requestData);
        this.$message.success(this.$T("编辑机组成功"));

        // 重新加载列表
        this.loadUnits();
      } catch (error) {
        this.$message.error(this.$T("编辑机组失败"));
      }
    },

    // 删除
    handleDelete(row) {
      this.$confirm(
        this.$T("确定要删除该机组吗？删除后与资源的绑定关系也将解除。"),
        this.$T("提示"),
        {
          confirmButtonText: this.$T("确定"),
          cancelButtonText: this.$T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          try {
            await deleteUnit(row.id);
            this.$message.success(this.$T("删除机组成功"));

            // 重新加载列表
            this.loadUnits();
          } catch (error) {
            this.$message.error(this.$T("删除机组失败"));
          }
        })
        .catch(() => {
          // 取消操作
        });
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
      this.loadUnits();
    },

    // 处理资源解绑事件
    handleResourceUnbound() {
      // 重新加载机组列表以更新资源数量
      this.loadUnits();
    }
  },

  watch: {
    // 监听类型筛选变化
    selectedType() {
      this.currentPage = 1;
      this.loadUnits();
    },

    // 监听搜索关键词变化
    searchKeyword() {
      this.currentPage = 1;
      this.loadUnits();
    }
  }
};
</script>

<style lang="scss" scoped>
.aggregation-container {
  @include background_color(BG1);
  @include padding(J4);

  .search-filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--J3);
    @include margin_bottom(J3);

    .search-filter-left {
      display: flex;
      align-items: center;
      gap: var(--J3);
      .search-input {
        width: 240px;
      }
      .filter-select {
        width: 240px;
      }
    }
  }

  .table-section {
    @include background_color(BG1);
    border-radius: var(--Ra);
    overflow: hidden;
    @include margin_bottom(J3);

    .data-table {
      width: 100%;

      .action-buttons-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--J4);

        .action-btn {
          @include font_size(Aa);
          padding: 0;

          &.detail-btn,
          &.edit-btn {
            @include font_color(ZS);
          }

          &.delete-btn {
            @include font_color(Sta3);
          }
        }
      }
    }
  }

  .pagination-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(--J3);

    .count-number {
      @include font_color(ZS);
    }

    .page-size-select {
      width: 100px;
    }
  }
}
</style>
