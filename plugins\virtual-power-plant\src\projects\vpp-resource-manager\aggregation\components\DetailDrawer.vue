<template>
  <el-drawer
    class="detail-drawer"
    :title="$T('详情')"
    :visible.sync="drawerVisible"
    destroy-on-close
    size="960px"
  >
    <div class="drawer-content">
      <!-- 基础信息 -->

      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <div class="label">{{ $T("机组名称") }}</div>
            <div>{{ detailData.name || "--" }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">{{ $T("机组类型") }}</div>
            <div>{{ detailData.type || "--" }}</div>
          </div>
        </el-col>
      </el-row>

      <!-- 资源列表 -->
      <div class="resource-list-section">
        <div class="section-title">{{ detailData.type + $T("资源列表") }}</div>

        <!-- 资源表格 -->
        <div class="resource-table-wrapper">
          <el-table
            :data="currentPageResourceData"
            class="resource-table"
            max-height="440"
          >
            <el-table-column
              prop="index"
              :label="$T('序号')"
              width="60"
              align="center"
            >
              <template slot-scope="scope">
                {{ String(scope.row.index).padStart(2, "0") }}
              </template>
            </el-table-column>

            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="140"
              show-overflow-tooltip
            />

            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="160"
              show-overflow-tooltip
            />

            <el-table-column
              prop="region"
              :label="$T('区域')"
              min-width="100"
            />

            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="120"
            />

            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="80"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T("是") : $T("否") }}
              </template>
            </el-table-column>

            <el-table-column :label="$T('操作')" width="80">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  class="unbind-btn"
                  @click="handleUnbind(scope.row)"
                >
                  {{ $T("解绑") }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <span>
            {{ $T("共") }}
            <span class="total-number">{{ totalResourceCount }}</span>
            {{ $T("个") }}
          </span>

          <el-select
            v-model="pageSize"
            class="page-size-select"
            @change="handlePageSizeChange"
          >
            <el-option
              v-for="size in pageSizeOptions"
              :key="size"
              :label="`${size}${$T('条/页')}`"
              :value="size"
            />
          </el-select>

          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :total="totalResourceCount"
            layout="prev, pager, next, jumper"
            @current-change="handlePageChange"
            class="pagination-component"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { unbindResource } from "@/api/resource-aggregation";

export default {
  name: "DetailDrawer",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50, 100],

      // 资源数据
      allResourceData: [],
      totalResourceCount: 0
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    },

    // 当前页资源数据
    currentPageResourceData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.allResourceData.slice(start, end);
    },
  },
  watch: {
    visible(newVal) {
      if (
        newVal &&
        this.detailData &&
        Object.keys(this.detailData).length > 0
      ) {
        this.loadResourceData();
      }
    },
    detailData(newVal) {
      if (newVal && Object.keys(newVal).length > 0 && this.visible) {
        this.loadResourceData();
      }
    }
  },
  methods: {
    // 加载资源数据
    loadResourceData() {
      if (this.detailData && this.detailData.bound_resources) {
        this.allResourceData = this.detailData.bound_resources.map(
          (item, index) => ({
            index: index + 1,
            resourceId: item.resourceId,
            resourceName: item.resourceName,
            region: item.region,
            capacity: item.capacity,
            directControl: item.directControl
          })
        );
        this.totalResourceCount = this.detailData.bound_resources.length;
      } else {
        this.allResourceData = [];
        this.totalResourceCount = 0;
      }
    },

    // 解绑操作
    handleUnbind(row) {
      this.$confirm(this.$T("确定要解绑该资源吗？"), this.$T("提示"), {
        confirmButtonText: this.$T("确定"),
        cancelButtonText: this.$T("取消"),
        type: "warning"
      })
        .then(async () => {
          try {
            await unbindResource(this.detailData.unit_id, row.resourceId);
            this.$message.success(this.$T("解绑成功"));

            // 从本地数据中移除
            const index = this.allResourceData.findIndex(
              item => item.resourceId === row.resourceId
            );
            if (index > -1) {
              this.allResourceData.splice(index, 1);
              this.totalResourceCount--;

              // 如果当前页没有数据了，回到上一页
              if (
                this.currentPageResourceData.length === 0 &&
                this.currentPage > 1
              ) {
                this.currentPage--;
              }
            }

            // 通知父组件刷新列表
            this.$emit("resource-unbound");
          } catch (error) {
            this.$message.error(this.$T("解绑资源失败"));
          }
        })
        .catch(() => {
          // 取消操作
        });
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-drawer {
  .drawer-content {
    @include padding(J4);
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--J4);

    .info-item {
      .label {
        @include font_color(T4);
      }
    }

    .resource-list-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--J3);

      .section-title {
        font-weight: 700;
      }

      .resource-table-wrapper {
        @include background_color(BG1);
        border-radius: var(--Ra);
        overflow: hidden;

        .resource-table {
          width: 100%;
          table-layout: fixed;

          .unbind-btn {
            @include font_color(ZS);
            padding: 0;
          }

          // 确保表格单元格内容不会溢出
          :deep(.el-table__body-wrapper) {
            overflow-x: hidden;
          }

          // 处理长文本的显示
          :deep(.cell) {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .pagination-wrapper {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: var(--J3);
        .total-number {
          @include font_color(ZS);
        }
        .page-size-select {
          width: 100px;
        }
      }
    }
  }
}
</style>
