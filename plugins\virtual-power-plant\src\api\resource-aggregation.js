/**
 * 资源聚合模块 API 接口
 * 
 * 功能说明：
 * - 机组创建与资源绑定
 * - 机组管理（查看、修改、删除）
 * - 资源绑定关系管理
 * - 统计与展示功能
 */
import fetch from "eem-base/utils/fetch";

const prefix = "/api/v1";

// ==================== 机组管理 ====================

/**
 * 创建新机组
 * @param {Object} data - 机组数据
 * @param {string} data.unit_name - 机组名称
 * @param {string} data.unit_type - 机组类型 (demand_response|peak_shaving|frequency_regulation)
 * @param {Array<string>} data.resource_ids - 绑定的资源ID列表
 * @returns {Promise} 请求Promise
 */
export function createUnit(data) {
  return fetch({
    url: `${prefix}/resource/agg/units`,
    method: "POST",
    data
  });
}

/**
 * 删除机组
 * @param {number|string} unitId - 机组ID
 * @returns {Promise} 请求Promise
 */
export function deleteUnit(unitId) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}`,
    method: "DELETE"
  });
}

/**
 * 更新机组信息
 * @param {number|string} unitId - 机组ID
 * @param {Object} data - 更新数据
 * @param {string} data.unit_name - 机组名称
 * @param {Array<string>} data.resource_ids - 绑定的资源ID列表
 * @returns {Promise} 请求Promise
 */
export function updateUnit(unitId, data) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}`,
    method: "POST",
    data
  });
}

/**
 * 获取机组详情
 * @param {number|string} unitId - 机组ID
 * @returns {Promise} 请求Promise
 */
export function getUnit(unitId) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}`,
    method: "GET"
  });
}

/**
 * 获取机组列表
 * @param {Object} params - 查询参数
 * @param {string} [params.unit_type] - 机组类型过滤
 * @param {number} [params.page] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @returns {Promise} 请求Promise
 */
export function listUnits(params) {
  return fetch({
    url: `${prefix}/resource/agg/units`,
    method: "GET",
    params
  });
}

// ==================== 资源管理 ====================

/**
 * 获取可绑定的资源列表
 * @param {Object} params - 查询参数
 * @param {string} params.unit_type - 机组类型（必填）
 * @param {number|string} [params.unit_id] - 机组ID（编辑时传入）
 * @param {string} params.district - 资源所属区域（必填）
 * @returns {Promise} 请求Promise
 */
export function getAvailableResources(params) {
  return fetch({
    url: `${prefix}/resource/agg/available-resources`,
    method: "GET",
    params
  });
}

/**
 * 从机组解绑单个资源
 * @param {number|string} unitId - 机组ID
 * @param {string} resourceId - 资源ID
 * @returns {Promise} 请求Promise
 */
export function unbindResource(unitId, resourceId) {
  return fetch({
    url: `${prefix}/resource/agg/units/${unitId}/resources/${resourceId}`,
    method: "DELETE"
  });
}

/**
 * 获取资源区域枚举值
 * @returns {Promise} 请求Promise
 */
export function getDistricts() {
  return fetch({
    url: `${prefix}/resource/agg/districts`,
    method: "GET"
  });
}